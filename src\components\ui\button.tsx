import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { textVariants } from "../Text";

const buttonVariants = cva(
  "inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-full font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive lowercase",
  {
    variants: {
      variant: {
        default:
          "bg-secondary-foreground text-white shadow-xs hocus:bg-primary-foreground",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border-2 border-secondary-foreground hocus:border-primary hocus:text-secondary",
        secondary:
          "bg-primary text-white shadow-xs hocus:bg-primary-foreground",
        ghost: "hocus:bg-primary-foreground hocus:text-accent-foreground",
        link: "underline-offset-4 underline decoration-1 hocus:text-primary !p-0",
      },
      size: {
        default: cn(
          textVariants({ size: "base" }),
          "px-4 py-1 has-[>svg]:px-3",
        ),
        xs: cn(textVariants({ size: "xs" }), "has-[>svg]:px-3"),
        sm: cn(
          textVariants({ size: "sm" }),
          "gap-1.5 px-4 py-1 has-[>svg]:px-2.5",
        ),
        lg: cn(textVariants({ size: "lg" }), "rounded-lg px-6 has-[>svg]:px-4"),
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
