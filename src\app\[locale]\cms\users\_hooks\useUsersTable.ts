"use client";

import { useId, useMemo, useRef, useState, useTransition } from "react";
import {
  type ColumnDef,
  type ColumnFiltersState,
  type FilterFn,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type PaginationState,
  type Row,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { UserType } from "@/app/[locale]/(auth)/model";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EllipsisIcon } from "lucide-react";
import {
  banUser,
  unbanUser,
  revokeAllUserSessions,
  removeUser,
} from "../actions";

// Multi-column filter function
const multiColumnFilterFn: FilterFn<UserType> = (row, columnId, value) => {
  const searchValue = String(value).toLowerCase();
  const firstName = String(row.original.firstName || "").toLowerCase();
  const lastName = String(row.original.lastName || "").toLowerCase();
  const email = String(row.original.email || "").toLowerCase();
  
  return (
    firstName.includes(searchValue) ||
    lastName.includes(searchValue) ||
    email.includes(searchValue) ||
    `${firstName} ${lastName}`.includes(searchValue)
  );
};

// Status filter function
const statusFilterFn: FilterFn<UserType> = (row, columnId, value) => {
  if (!value || !Array.isArray(value) || value.length === 0) return true;
  const banned = row.getValue(columnId) as boolean;
  return value.includes(String(banned));
};

// Helper function to get user status
function getUserStatus(
  user: UserType,
  t: ReturnType<typeof useTranslations<"CMS.users">>,
): { text: string; variant: "default" | "secondary" | "destructive" } {
  if (user.banned) {
    return { text: t("table.statusBanned"), variant: "destructive" };
  }
  return { text: t("table.statusActive"), variant: "default" };
}

// RowActions component
function RowActions({ row }: { row: Row<UserType> }) {
  const t = useTranslations("CMS.users");
  const [isPending, startTransition] = useTransition();

  const handleToggleStatus = () => {
    startTransition(async () => {
      try {
        const result = row.original.banned
          ? await unbanUser({ userId: row.original.id })
          : await banUser({ userId: row.original.id });

        if (result.success) {
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error(t("actions.genericError"));
      }
    });
  };

  const handleRevokeSession = () => {
    startTransition(async () => {
      try {
        const result = await revokeAllUserSessions({ userId: row.original.id });
        if (result.success) {
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error(t("actions.genericError"));
      }
    });
  };

  const handleDelete = () => {
    startTransition(async () => {
      try {
        const result = await removeUser({ userId: row.original.id });
        if (result.success) {
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      } catch (error) {
        toast.error(t("actions.genericError"));
      }
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none"
            aria-label={t("table.actions")}
            disabled={isPending}
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={handleToggleStatus} disabled={isPending}>
            <span>
              {row.original.banned ? t("actions.unban") : t("actions.ban")}
            </span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleRevokeSession} disabled={isPending}>
            <span>{t("actions.revokeSessions")}</span>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDelete}
          disabled={isPending}
          className="text-destructive focus:text-destructive"
        >
          <span>{t("actions.remove")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface UseUsersTableProps {
  initialUsers: UserType[];
  totalUsers: number;
  initialPage: number;
  initialLimit: number;
}

export const useUsersTable = ({
  initialUsers,
  totalUsers,
  initialPage,
  initialLimit,
}: UseUsersTableProps) => {
  const id = useId();
  const t = useTranslations("CMS.users");
  const [isPending, startTransition] = useTransition();
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: initialPage - 1,
    pageSize: initialLimit,
  });

  const inputRef = useRef<HTMLInputElement>(null);

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "name",
      desc: false,
    },
  ]);

  const [data, setData] = useState<UserType[]>(initialUsers);

  // Create columns function
  const createColumns = (): ColumnDef<UserType>[] => [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Selecionar todos"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Selecionar linha"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: t("table.name"),
      accessorKey: "firstName",
      cell: ({ row }) => (
        <div className="font-medium">
          {`${row.original.firstName} ${row.original.lastName}`.trim() ||
            t("table.notAvailable")}
        </div>
      ),
      size: 200,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
    },
    {
      header: t("table.email"),
      accessorKey: "email",
      size: 250,
    },
    {
      header: t("table.role"),
      accessorKey: "role",
      cell: ({ row }) => (
        <Badge
          variant={row.getValue("role") === "admin" ? "default" : "secondary"}
        >
          {row.getValue("role")}
        </Badge>
      ),
      size: 100,
    },
    {
      header: t("table.status"),
      accessorKey: "banned",
      cell: ({ row }) => {
        const status = getUserStatus(row.original, t);
        return <Badge variant={status.variant}>{status.text}</Badge>;
      },
      size: 150,
      filterFn: statusFilterFn,
    },
    {
      id: "actions",
      header: () => <span className="sr-only">Ações</span>,
      cell: ({ row }) => <RowActions row={row} />,
      size: 60,
      enableHiding: false,
    },
  ];

  const columns = useMemo(() => createColumns(), [t]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  // Bulk action handlers
  const handleDeleteRows = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    startTransition(async () => {
      try {
        for (const row of selectedRows) {
          const result = await removeUser({ userId: row.original.id });
          if (!result.success) {
            toast.error(result.message);
            return;
          }
        }

        const updatedData = data.filter(
          (item) => !selectedRows.some((row) => row.original.id === item.id),
        );
        setData(updatedData);
        table.resetRowSelection();
        toast.success(t("actions.removeUserSuccess"));
      } catch (error) {
        toast.error(t("actions.genericError"));
      }
    });
  };

  const handleBulkBlock = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    startTransition(async () => {
      try {
        for (const row of selectedRows) {
          const user = row.original;
          const result = user.banned
            ? await unbanUser({ userId: user.id })
            : await banUser({ userId: user.id });

          if (!result.success) {
            toast.error(result.message);
            return;
          }
        }

        const updatedData = data.map((item) => {
          const isSelected = selectedRows.some(
            (row) => row.original.id === item.id,
          );
          if (isSelected) {
            return {
              ...item,
              banned: !item.banned,
            };
          }
          return item;
        });
        setData(updatedData);
        table.resetRowSelection();
        toast.success("Ações executadas com sucesso");
      } catch (error) {
        toast.error(t("actions.genericError"));
      }
    });
  };

  const handleBulkRevokeSession = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    startTransition(async () => {
      try {
        for (const row of selectedRows) {
          const result = await revokeAllUserSessions({
            userId: row.original.id,
          });
          if (!result.success) {
            toast.error(result.message);
            return;
          }
        }
        table.resetRowSelection();
        toast.success(t("actions.revokeSessionsSuccess"));
      } catch (error) {
        toast.error(t("actions.genericError"));
      }
    });
  };

  // Get block button text based on selected rows
  const getBlockButtonText = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const hasActiveUsers = selectedRows.some((row) => !row.original.banned);
    const hasInactiveUsers = selectedRows.some((row) => row.original.banned);

    if (hasActiveUsers && hasInactiveUsers) {
      return "Alternar Status";
    } else if (hasActiveUsers) {
      return t("actions.ban");
    } else {
      return t("actions.unban");
    }
  };

  // Status filter handlers
  const handleStatusChange = (checked: boolean, value: string) => {
    const filterValue = table.getColumn("banned")?.getFilterValue() as string[];
    const newFilterValue = filterValue ? [...filterValue] : [];

    if (checked) {
      newFilterValue.push(value);
    } else {
      const index = newFilterValue.indexOf(value);
      if (index > -1) {
        newFilterValue.splice(index, 1);
      }
    }

    table
      .getColumn("banned")
      ?.setFilterValue(newFilterValue.length ? newFilterValue : undefined);
  };



  // Get unique status values
  const uniqueStatusValues = useMemo(() => {
    const statusColumn = table.getColumn("banned");
    if (!statusColumn) return [];
    const values = Array.from(statusColumn.getFacetedUniqueValues().keys());
    return values.sort();
  }, [table.getColumn("banned")?.getFacetedUniqueValues()]);

  // Get counts for each status
  const statusCounts = useMemo(() => {
    const statusColumn = table.getColumn("banned");
    if (!statusColumn) return new Map();
    return statusColumn.getFacetedUniqueValues();
  }, [table.getColumn("banned")?.getFacetedUniqueValues()]);

  const selectedStatuses = useMemo(() => {
    const filterValue = table.getColumn("banned")?.getFilterValue() as string[];
    return filterValue ?? [];
  }, [table.getColumn("banned")?.getFilterValue()]);

  return {
    // Table instance and data
    table,
    data,
    setData,

    // State
    isPending,
    columnFilters,
    columnVisibility,
    pagination,
    sorting,

    // Refs
    inputRef,

    // Handlers
    handleDeleteRows,
    handleBulkBlock,
    handleBulkRevokeSession,
    handleStatusChange,
    getBlockButtonText,

    // Computed values
    uniqueStatusValues,
    statusCounts,
    selectedStatuses,

    // Utilities
    id,
    t,
    startTransition,
    getUserStatus,
  };
};
